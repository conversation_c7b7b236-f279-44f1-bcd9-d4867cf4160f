# Server Configuration
PORT=8080
ENVIRONMENT=development

# Database Configuration
# For local PostgreSQL:
# DATABASE_URL=postgres://user:password@localhost:5432/enquiry_db?sslmode=disable
# For Supabase:
DATABASE_URL=postgresql://postgres.your_project_id:<EMAIL>:5432/postgres

# Database Connection Pool Settings (Optional - will use defaults if not set)
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=300s
DB_CONN_MAX_IDLE_TIME=60s

# Application Settings
LOG_LEVEL=info
API_TIMEOUT=30s

# JWT Authentication Settings
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_ISSUER=enquiry-management-system
JWT_AUDIENCE=enquiry-management-api
JWT_ALGORITHM=HS256

# For Docker Compose (if using local PostgreSQL)
POSTGRES_USER=user
POSTGRES_PASSWORD=password
POSTGRES_DB=enquiry_db
