package database

import (
	"fmt"
	"log"
	"time"

	"enquiry-management-system/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type DB struct {
	*gorm.DB
}

func NewConnection(cfg *config.Config) (*DB, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.Name,
		cfg.Database.SSLMode,
	)

	// Configure GORM logger
	var gormLogger logger.Interface
	if cfg.Server.Env == "development" {
		gormLogger = logger.Default.LogMode(logger.Info)
	} else {
		gormLogger = logger.Default.LogMode(logger.Error)
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	log.Println("Successfully connected to database with GORM")

	return &DB{db}, nil
}

func (db *DB) Close() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// RunMigrations - COMMENTED OUT FOR MANUAL MIGRATION HANDLING
// func (db *DB) RunMigrations() error {
// 	log.Println("Running GORM auto-migrations...")

// 	// Auto-migrate all models
// 	err := db.AutoMigrate(
// 		&models.User{},
// 		&models.Enquiry{},
// 		&models.Note{},
// 		&models.Activity{},
// 	)
// 	if err != nil {
// 		return fmt.Errorf("failed to run auto-migrations: %w", err)
// 	}

// 	// Seed default data
// 	if err := db.seedDefaultData(); err != nil {
// 		return fmt.Errorf("failed to seed default data: %w", err)
// 	}

// 	log.Println("Successfully completed migrations and seeding")
// 	return nil
// }
