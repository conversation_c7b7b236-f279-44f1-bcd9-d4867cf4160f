# Notes & Activity API Documentation

## Overview
The Notes API provides endpoints for managing notes associated with enquiries. Notes allow users to add comments, updates, and observations to enquiry records.

## Database Setup

Before using the notes endpoints, you need to create the notes table in your database:

```sql
-- Run the migration script
\i backend/migrations/001_create_notes_table.sql
```

Or manually execute the SQL commands in `backend/migrations/001_create_notes_table.sql`.

## Endpoints

### 1. Create Note
Add a new note to an enquiry.

**Endpoint:** `POST /api/v1/enquiries/{enquiry_id}/notes`

**Request Body:**
```json
{
  "content": "This is a note about the enquiry",
  "user_id": "123e4567-e89b-12d3-a456-************"
}
```

**Response (201 Created):**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "enquiry_id": "123e4567-e89b-12d3-a456-************",
  "user_id": "123e4567-e89b-12d3-a456-************",
  "content": "This is a note about the enquiry",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "agent"
  }
}
```

### 2. Get Notes History
Retrieve all notes for an enquiry, ordered by creation date (newest first).

**Endpoint:** `GET /api/v1/enquiries/{enquiry_id}/notes`

**Response (200 OK):**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "enquiry_id": "123e4567-e89b-12d3-a456-************",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "content": "Latest note about the enquiry",
    "created_at": "2024-01-15T14:30:00Z",
    "updated_at": "2024-01-15T14:30:00Z",
    "user": {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "agent"
    }
  },
  {
    "id": "123e4567-e89b-12d3-a456-426614174003",
    "enquiry_id": "123e4567-e89b-12d3-a456-************",
    "user_id": "123e4567-e89b-12d3-a456-426614174004",
    "content": "Earlier note about the enquiry",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "user": {
      "id": "123e4567-e89b-12d3-a456-426614174004",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "role": "admin"
    }
  }
]
```

### 3. Delete Note
Delete a specific note from an enquiry.

**Endpoint:** `DELETE /api/v1/enquiries/{enquiry_id}/notes/{note_id}`

**Response (200 OK):**
```json
{
  "message": "Note deleted successfully"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "message": "Invalid request body"
}
```

### 404 Not Found
```json
{
  "message": "Enquiry not found"
}
```

### 500 Internal Server Error
```json
{
  "message": "Database error"
}
```

## Usage Examples

### Using cURL

**Create a note:**
```bash
curl -X POST http://localhost:8080/api/v1/enquiries/123e4567-e89b-12d3-a456-************/notes \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Customer called to ask about pricing",
    "user_id": "123e4567-e89b-12d3-a456-************"
  }'
```

**Get notes history:**
```bash
curl http://localhost:8080/api/v1/enquiries/123e4567-e89b-12d3-a456-************/notes
```

**Delete a note:**
```bash
curl -X DELETE http://localhost:8080/api/v1/enquiries/123e4567-e89b-12d3-a456-************/notes/123e4567-e89b-12d3-a456-************
```

## Notes

1. **No Authentication**: Currently, these endpoints do not require authentication (as per project requirements).
2. **UUID Format**: All IDs must be valid UUIDs.
3. **Validation**: Content and user_id are required fields when creating a note.
4. **Ordering**: Notes are returned in descending order by creation date (newest first).
5. **User Information**: The response includes user details for each note.
6. **Soft Deletes**: Notes use GORM's soft delete functionality (deleted_at field).

---

# Activity API Documentation

## Overview
The Activity API provides endpoints for retrieving the activity timeline of enquiries. Activities are automatically created when actions are performed on enquiries and notes.

## Database Setup

Before using the activity endpoints, you need to create the activities table:

```sql
-- Run the migration script
\i backend/migrations/002_create_activities_table.sql
```

## Activity Types

The system automatically tracks the following activity types:

- **Enquiry Created** - When a new enquiry is submitted
- **Status Changed** - When enquiry status is updated
- **Priority Changed** - When enquiry priority is modified
- **Assignment Changed** - When enquiry is assigned/unassigned
- **Note Added** - When a note is added to an enquiry
- **Note Deleted** - When a note is removed from an enquiry
- **Enquiry Updated** - When other enquiry details are modified

## Endpoints

### Get Activity Timeline
Retrieve all activities for an enquiry, ordered by creation date (newest first).

**Endpoint:** `GET /api/v1/enquiries/{enquiry_id}/activities`

**Response (200 OK):**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "enquiry_id": "123e4567-e89b-12d3-a456-************",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "type": "Status Changed",
    "description": "Status changed from 'New' to 'In Progress'",
    "metadata": "{\"old_status\":\"New\",\"new_status\":\"In Progress\"}",
    "created_at": "2024-01-15T14:30:00Z",
    "updated_at": "2024-01-15T14:30:00Z",
    "user": {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "agent"
    }
  },
  {
    "id": "123e4567-e89b-12d3-a456-426614174003",
    "enquiry_id": "123e4567-e89b-12d3-a456-************",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "type": "Note Added",
    "description": "Added a note: Customer called to ask about pricing",
    "metadata": "{\"note_content\":\"Customer called to ask about pricing\"}",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "user": {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "agent"
    }
  }
]
```

## Usage Examples

### Using cURL

**Get activity timeline:**
```bash
curl http://localhost:8080/api/v1/enquiries/123e4567-e89b-12d3-a456-************/activities
```

## Automatic Activity Creation

Activities are automatically created when:

1. **Creating an enquiry** - Creates "Enquiry Created" activity
2. **Updating enquiry status** - Creates "Status Changed" activity
3. **Updating enquiry priority** - Creates "Priority Changed" activity
4. **Assigning/unassigning enquiry** - Creates "Assignment Changed" activity
5. **Adding a note** - Creates "Note Added" activity
6. **Deleting a note** - Creates "Note Deleted" activity
7. **Updating other enquiry fields** - Creates "Enquiry Updated" activity

## Activity Metadata

Each activity can include metadata in JSON format containing additional context:

- **Status/Priority Changes**: Old and new values
- **Assignment Changes**: Old and new assignee information
- **Note Activities**: Note content (for additions)
- **General Updates**: List of changed fields

## Frontend Integration

The activity data is designed to work seamlessly with the existing `EnquiryActivity` React component:

```javascript
// The API response format matches the expected props
<EnquiryActivity activities={activitiesFromAPI} />
```

## Future Enhancements

When authentication is implemented, the following security features should be added:
- Only assigned users and admins can manage notes
- User context from authentication token
- Role-based access control
- Audit logging for note operations
- Real user tracking instead of placeholder system user
