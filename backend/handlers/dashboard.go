package handlers

import (
	"net/http"
	"strconv"
	"time"

	"enquiry-management-system/models"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type DashboardHandler struct {
	db *gorm.DB
}

func NewDashboardHandler(db *gorm.DB) *DashboardHandler {
	return &DashboardHandler{db: db}
}

// GetDashboardOverview returns combined dashboard data
func (h *DashboardHandler) GetDashboardOverview(c echo.Context) error {
	// Parse query parameters with defaults
	recentEnquiriesLimit := 10
	if limit := c.QueryParam("recent_enquiries_limit"); limit != "" {
		if parsed, err := strconv.Atoi(limit); err == nil && parsed > 0 && parsed <= 50 {
			recentEnquiriesLimit = parsed
		}
	}

	recentActivitiesLimit := 10
	if limit := c.QueryParam("recent_activities_limit"); limit != "" {
		if parsed, err := strconv.Atoi(limit); err == nil && parsed > 0 && parsed <= 50 {
			recentActivitiesLimit = parsed
		}
	}

	activitiesDays := 7
	if days := c.QueryParam("activities_days"); days != "" {
		if parsed, err := strconv.Atoi(days); err == nil && parsed > 0 && parsed <= 90 {
			activitiesDays = parsed
		}
	}

	// Get dashboard stats
	stats, err := h.getDashboardStats()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch dashboard stats")
	}

	// Get recent enquiries
	recentEnquiries, err := h.getRecentEnquiries(recentEnquiriesLimit)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch recent enquiries")
	}

	// Get recent activities
	recentActivities, err := h.getRecentActivities(recentActivitiesLimit, activitiesDays)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch recent activities")
	}

	// Combine all data
	overview := models.DashboardOverview{
		Stats:            stats,
		RecentEnquiries:  recentEnquiries,
		RecentActivities: recentActivities,
	}

	return c.JSON(http.StatusOK, overview)
}

// getDashboardStats calculates dashboard statistics
func (h *DashboardHandler) getDashboardStats() (models.DashboardStats, error) {
	var stats models.DashboardStats

	// Total enquiries
	if err := h.db.Model(&models.Enquiry{}).Count(&stats.TotalEnquiries).Error; err != nil {
		return stats, err
	}

	// New today (enquiries created today)
	today := time.Now().Format("2006-01-02")
	if err := h.db.Model(&models.Enquiry{}).
		Where("DATE(created_at) = ?", today).
		Count(&stats.NewToday).Error; err != nil {
		return stats, err
	}

	// In Progress enquiries
	if err := h.db.Model(&models.Enquiry{}).
		Where("status = ?", "In Progress").
		Count(&stats.InProgress).Error; err != nil {
		return stats, err
	}

	// Closed enquiries
	if err := h.db.Model(&models.Enquiry{}).
		Where("status = ?", "Closed").
		Count(&stats.Closed).Error; err != nil {
		return stats, err
	}

	return stats, nil
}

// getRecentEnquiries fetches recent enquiries for dashboard
func (h *DashboardHandler) getRecentEnquiries(limit int) ([]models.RecentEnquiry, error) {
	var enquiries []models.Enquiry
	var recentEnquiries []models.RecentEnquiry

	// Fetch recent enquiries with assigned user information
	if err := h.db.Order("created_at DESC").Limit(limit).Find(&enquiries).Error; err != nil {
		return recentEnquiries, err
	}

	// Convert to dashboard format
	for _, enquiry := range enquiries {
		var assignedToName string
		
		// Get assigned user name if assigned
		if enquiry.AssignedToID != nil {
			var user models.User
			if err := h.db.First(&user, "id = ?", *enquiry.AssignedToID).Error; err == nil {
				assignedToName = user.Name
			}
		}

		recentEnquiry := models.RecentEnquiry{
			ID:          enquiry.ID,
			Name:        enquiry.Name,
			AssignedTo:  assignedToName,
			Status:      enquiry.Status,
			SubmittedOn: enquiry.CreatedAt.Format("January 2, 2006"),
			Slug:        enquiry.Slug,
		}

		recentEnquiries = append(recentEnquiries, recentEnquiry)
	}

	return recentEnquiries, nil
}

// getRecentActivities fetches recent activities for dashboard
func (h *DashboardHandler) getRecentActivities(limit int, days int) ([]models.RecentActivity, error) {
	var activities []models.Activity
	var recentActivities []models.RecentActivity

	// Calculate date range
	since := time.Now().AddDate(0, 0, -days)

	// Fetch recent activities within date range
	if err := h.db.Where("created_at >= ?", since).
		Order("created_at DESC").
		Limit(limit).
		Find(&activities).Error; err != nil {
		return recentActivities, err
	}

	// Convert to dashboard format with additional context
	for _, activity := range activities {
		var enquiryName string
		var userName string

		// Get enquiry name
		var enquiry models.Enquiry
		if err := h.db.First(&enquiry, "id = ?", activity.EnquiryID).Error; err == nil {
			enquiryName = enquiry.Name
		}

		// Get user name
		var user models.User
		if err := h.db.First(&user, "id = ?", activity.UserID).Error; err == nil {
			userName = user.Name
		} else {
			userName = "System" // Fallback for system activities or deleted users
		}

		recentActivity := models.RecentActivity{
			ID:          activity.ID,
			Type:        activity.Type,
			Description: activity.Description,
			Timestamp:   activity.CreatedAt.Format(time.RFC3339),
			EnquiryName: enquiryName,
			UserName:    userName,
		}

		recentActivities = append(recentActivities, recentActivity)
	}

	return recentActivities, nil
}
