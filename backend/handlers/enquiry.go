package handlers

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"enquiry-management-system/middleware"
	"enquiry-management-system/models"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type EnquiryHandler struct {
	db *gorm.DB
}

func NewEnquiryHandler(db *gorm.DB) *EnquiryHandler {
	return &EnquiryHandler{db: db}
}

func (h *EnquiryHandler) GetEnquiries(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	var filters models.EnquiryFilters
	if err := c.Bind(&filters); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid query parameters")
	}

	// Set defaults
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.Limit <= 0 {
		filters.Limit = 10
	}

	// Build query with GORM
	query := h.db.Model(&models.Enquiry{})

	// Role-based access control
	if user.IsAgent() {
		// Agents can only see enquiries assigned to them
		query = query.Where("assigned_to_id = ?", user.ID)
	}
	// Managers and Admins can see all enquiries (no additional filter)

	// Add filters
	if filters.Search != "" {
		searchTerm := "%" + filters.Search + "%"
		query = query.Where("name ILIKE ? OR email ILIKE ? OR description ILIKE ?", searchTerm, searchTerm, searchTerm)
	}

	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}

	if filters.Priority != "" {
		query = query.Where("priority = ?", filters.Priority)
	}

	if filters.AssignedToID != "" {
		// Parse UUID for assigned_to_id filter
		if assignedToID, err := uuid.Parse(filters.AssignedToID); err == nil {
			// Agents cannot filter by other users' assignments
			if user.IsAgent() && assignedToID != user.ID {
				return echo.NewHTTPError(http.StatusForbidden, "Agents can only view their own assigned enquiries")
			}
			query = query.Where("assigned_to_id = ?", assignedToID)
		}
	}

	// Get total count for pagination
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to count enquiries")
	}

	// Apply pagination and ordering
	var enquiries []models.Enquiry
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Order("submitted_on DESC").Limit(filters.Limit).Offset(offset).Find(&enquiries).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch enquiries")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"data":  enquiries,
		"total": total,
		"page":  filters.Page,
		"limit": filters.Limit,
	})
}

func (h *EnquiryHandler) GetEnquiry(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID")
	}

	// Build query with role-based access control
	query := h.db.Where("id = ?", id)

	// Agents can only view enquiries assigned to them
	if user.IsAgent() {
		query = query.Where("assigned_to_id = ?", user.ID)
	}
	// Managers and Admins can view all enquiries

	var enquiry models.Enquiry
	if err := query.First(&enquiry).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			if user.IsAgent() {
				return echo.NewHTTPError(http.StatusForbidden, "You can only view enquiries assigned to you")
			}
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch enquiry")
	}

	return c.JSON(http.StatusOK, enquiry)
}

func (h *EnquiryHandler) CreateEnquiry(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	var req models.CreateEnquiryRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Validate required fields
	if req.Name == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Name is required")
	}
	if req.Email == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Email is required")
	}

	// Validate email format
	if !isValidEmail(req.Email) {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid email format")
	}

	// Generate slug from name
	slug := generateSlug(req.Name)

	// Set defaults and validate optional fields
	if req.Priority == "" {
		req.Priority = "Medium"
	} else if !isValidPriority(req.Priority) {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid priority. Must be one of: High, Medium, Low")
	}

	if req.Source == "" {
		req.Source = "Website Form"
	} else if !isValidSource(req.Source) {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid source. Must be one of: Website Form, Phone Call, Email, Referral, Social Media")
	}

	enquiry := models.Enquiry{
		Slug:        slug,
		Name:        req.Name,
		Email:       req.Email,
		Phone:       req.Phone,
		Priority:    req.Priority,
		Source:      req.Source,
		Description: req.Description,
		SubmittedOn: time.Now(),
	}

	if err := h.db.Create(&enquiry).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create enquiry")
	}

	// Create activity for enquiry creation using authenticated user
	activityHandler := NewActivityHandler(h.db)
	activityHandler.TrackEnquiryCreated(enquiry.ID, user.ID, enquiry.Name)

	return c.JSON(http.StatusCreated, enquiry)
}

func (h *EnquiryHandler) UpdateEnquiry(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID")
	}

	var req models.UpdateEnquiryRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Build query with role-based access control
	query := h.db.Where("id = ?", id)

	// Agents can only update enquiries assigned to them
	if user.IsAgent() {
		query = query.Where("assigned_to_id = ?", user.ID)
	}
	// Managers and Admins can update all enquiries

	// Find the enquiry
	var enquiry models.Enquiry
	if err := query.First(&enquiry).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			if user.IsAgent() {
				return echo.NewHTTPError(http.StatusForbidden, "You can only update enquiries assigned to you")
			}
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to find enquiry")
	}

	// Store original values for activity tracking
	originalStatus := enquiry.Status
	originalPriority := enquiry.Priority
	var originalAssignee string
	if enquiry.AssignedToID != nil {
		var assignedUser models.User
		if err := h.db.First(&assignedUser, "id = ?", *enquiry.AssignedToID).Error; err == nil {
			originalAssignee = assignedUser.Name
		}
	}

	// Update only non-empty fields (with validation)
	updates := make(map[string]interface{})
	var activityChanges []string

	// Use authenticated user for activity tracking
	activityHandler := NewActivityHandler(h.db)

	// AssignedToID - only admins and managers can assign/reassign enquiries
	if req.AssignedToID != nil {
		if user.IsAgent() {
			return echo.NewHTTPError(http.StatusForbidden, "Agents cannot assign or reassign enquiries")
		}

		updates["assigned_to_id"] = req.AssignedToID

		// Track assignment change
		var newAssignee string
		if *req.AssignedToID != uuid.Nil {
			var assignedUser models.User
			if err := h.db.First(&assignedUser, "id = ?", *req.AssignedToID).Error; err == nil {
				newAssignee = assignedUser.Name
			}
		}

		if originalAssignee != newAssignee {
			activityHandler.TrackAssignmentChanged(enquiry.ID, user.ID, originalAssignee, newAssignee)
			activityChanges = append(activityChanges, "assignment")
		}
	}

	// Status - only update if non-empty and valid
	if strings.TrimSpace(req.Status) != "" {
		status := strings.TrimSpace(req.Status)
		if !isValidStatus(status) {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid status. Must be one of: New, In Progress, Closed")
		}
		if status != originalStatus {
			updates["status"] = status
			activityHandler.TrackStatusChanged(enquiry.ID, user.ID, originalStatus, status)
			activityChanges = append(activityChanges, "status")
		}
	}

	// Priority - only admins and managers can change priority
	if strings.TrimSpace(req.Priority) != "" {
		if user.IsAgent() {
			return echo.NewHTTPError(http.StatusForbidden, "Agents cannot change enquiry priority")
		}

		priority := strings.TrimSpace(req.Priority)
		if !isValidPriority(priority) {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid priority. Must be one of: High, Medium, Low")
		}
		if priority != originalPriority {
			updates["priority"] = priority
			activityHandler.TrackPriorityChanged(enquiry.ID, user.ID, originalPriority, priority)
			activityChanges = append(activityChanges, "priority")
		}
	}

	if len(updates) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, "No fields to update")
	}

	// Perform update
	if err := h.db.Model(&enquiry).Updates(updates).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update enquiry")
	}

	// Create general update activity if there were other changes
	if len(activityChanges) > 0 {
		changesStr := strings.Join(activityChanges, ", ")
		activityHandler.TrackEnquiryUpdated(enquiry.ID, user.ID, changesStr)
	}

	return c.JSON(http.StatusOK, enquiry)
}

func (h *EnquiryHandler) DeleteEnquiry(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Only admins can delete enquiries
	if !user.IsAdmin() {
		return echo.NewHTTPError(http.StatusForbidden, "Only administrators can delete enquiries")
	}

	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID")
	}

	// Check if enquiry exists before deletion
	var enquiry models.Enquiry
	if err := h.db.Where("id = ?", id).First(&enquiry).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to find enquiry")
	}

	// Perform soft delete
	result := h.db.Where("id = ?", id).Delete(&models.Enquiry{})
	if result.Error != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete enquiry")
	}

	if result.RowsAffected == 0 {
		return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Enquiry deleted successfully"})
}

// Helper functions
func generateSlug(name string) string {
	// Simple slug generation - replace spaces with hyphens and convert to lowercase
	slug := strings.ToLower(strings.ReplaceAll(name, " ", "-"))
	// Add UUID suffix to ensure uniqueness
	uniqueID := uuid.New().String()[:8]
	return fmt.Sprintf("%s-%s", slug, uniqueID)
}

func isValidEmail(email string) bool {
	// Simple email validation regex
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

func isValidSector(sector string) bool {
	validSectors := []string{"College", "Business"}
	for _, validSector := range validSectors {
		if sector == validSector {
			return true
		}
	}
	return false
}

func isValidPriority(priority string) bool {
	validPriorities := []string{"High", "Medium", "Low"}
	for _, validPriority := range validPriorities {
		if priority == validPriority {
			return true
		}
	}
	return false
}

func isValidSource(source string) bool {
	validSources := []string{"Website Form", "Phone Call", "Email", "Referral", "Social Media"}
	for _, validSource := range validSources {
		if source == validSource {
			return true
		}
	}
	return false
}

func isValidStatus(status string) bool {
	validStatuses := []string{"New", "In Progress", "Closed"}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}
