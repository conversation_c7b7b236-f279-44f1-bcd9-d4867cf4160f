package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"enquiry-management-system/models"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// Auto-migrate the schema
	db.AutoMigrate(&models.User{}, &models.Enquiry{}, &models.Note{})

	return db
}

func createTestData(db *gorm.DB) (models.User, models.Enquiry) {
	// Create test user
	user := models.User{
		Name:   "Test User",
		Email:  "<EMAIL>",
		Role:   "agent",
		Status: "active",
	}
	db.Create(&user)

	// Create test enquiry
	enquiry := models.Enquiry{
		Slug:        "test-enquiry",
		Name:        "Test Enquiry",
		Email:       "<EMAIL>",
		Phone:       "1234567890",
		Status:      "New",
		Priority:    "Medium",
		Source:      "Website Form",
		Description: "Test enquiry description",
	}
	db.Create(&enquiry)

	return user, enquiry
}

func TestCreateNote(t *testing.T) {
	// Setup
	db := setupTestDB()
	user, enquiry := createTestData(db)
	handler := NewNoteHandler(db)

	// Create request
	reqBody := models.CreateNoteRequest{
		Content: "This is a test note",
		UserID:  user.ID.String(),
	}
	jsonBody, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(jsonBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()

	e := echo.New()
	c := e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(enquiry.ID.String())

	// Execute
	err := handler.CreateNote(c)

	// Assert
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if rec.Code != http.StatusCreated {
		t.Fatalf("Expected status %d, got %d", http.StatusCreated, rec.Code)
	}

	var response models.NoteResponse
	json.Unmarshal(rec.Body.Bytes(), &response)

	if response.Content != reqBody.Content {
		t.Fatalf("Expected content %s, got %s", reqBody.Content, response.Content)
	}

	if response.UserID != user.ID {
		t.Fatalf("Expected user ID %s, got %s", user.ID, response.UserID)
	}
}

func TestGetNotes(t *testing.T) {
	// Setup
	db := setupTestDB()
	user, enquiry := createTestData(db)
	handler := NewNoteHandler(db)

	// Create test notes
	note1 := models.Note{
		EnquiryID: enquiry.ID,
		UserID:    user.ID,
		Content:   "First note",
	}
	note2 := models.Note{
		EnquiryID: enquiry.ID,
		UserID:    user.ID,
		Content:   "Second note",
	}
	db.Create(&note1)
	db.Create(&note2)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()

	e := echo.New()
	c := e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(enquiry.ID.String())

	// Execute
	err := handler.GetNotes(c)

	// Assert
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if rec.Code != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, rec.Code)
	}

	var response []models.NoteResponse
	json.Unmarshal(rec.Body.Bytes(), &response)

	if len(response) != 2 {
		t.Fatalf("Expected 2 notes, got %d", len(response))
	}

	// Notes should be ordered by created_at DESC (newest first)
	if response[0].Content != "Second note" {
		t.Fatalf("Expected first note to be 'Second note', got %s", response[0].Content)
	}
}

func TestDeleteNote(t *testing.T) {
	// Setup
	db := setupTestDB()
	user, enquiry := createTestData(db)
	handler := NewNoteHandler(db)

	// Create test note
	note := models.Note{
		EnquiryID: enquiry.ID,
		UserID:    user.ID,
		Content:   "Note to be deleted",
	}
	db.Create(&note)

	// Create request
	req := httptest.NewRequest(http.MethodDelete, "/", nil)
	rec := httptest.NewRecorder()

	e := echo.New()
	c := e.NewContext(req, rec)
	c.SetParamNames("id", "note_id")
	c.SetParamValues(enquiry.ID.String(), note.ID.String())

	// Execute
	err := handler.DeleteNote(c)

	// Assert
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if rec.Code != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, rec.Code)
	}

	// Verify note is soft deleted
	var deletedNote models.Note
	result := db.Unscoped().First(&deletedNote, note.ID)
	if result.Error != nil {
		t.Fatalf("Note should still exist in database: %v", result.Error)
	}

	if deletedNote.DeletedAt.Time.IsZero() {
		t.Fatalf("Note should be soft deleted")
	}
}

func TestCreateNoteInvalidEnquiry(t *testing.T) {
	// Setup
	db := setupTestDB()
	user, _ := createTestData(db)
	handler := NewNoteHandler(db)

	// Create request with invalid enquiry ID
	reqBody := models.CreateNoteRequest{
		Content: "This is a test note",
		UserID:  user.ID.String(),
	}
	jsonBody, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(jsonBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()

	e := echo.New()
	c := e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(uuid.New().String()) // Random UUID that doesn't exist

	// Execute
	err := handler.CreateNote(c)

	// Assert
	if err == nil {
		t.Fatalf("Expected error for invalid enquiry ID")
	}

	httpError, ok := err.(*echo.HTTPError)
	if !ok {
		t.Fatalf("Expected HTTPError, got %T", err)
	}

	if httpError.Code != http.StatusNotFound {
		t.Fatalf("Expected status %d, got %d", http.StatusNotFound, httpError.Code)
	}
}
