package handlers

import (
	"net/http"
	"strings"
	"time"

	"enquiry-management-system/middleware"
	"enquiry-management-system/models"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type UserHandler struct {
	db *gorm.DB
}

func NewUserHandler(db *gorm.DB) *UserHandler {
	return &UserHandler{db: db}
}

func (h *UserHandler) GetMe(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	var user models.User
	if err := h.db.First(&user, "id = ?", authUser.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "User not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch user details")
	}

	return c.<PERSON>(http.StatusOK, user)
}

func (h *UserHandler) GetProfile(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	var user models.User
	if err := h.db.First(&user, "id = ?", authUser.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "User not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch user profile")
	}

	return c.JSON(http.StatusOK, user)
}

func (h *UserHandler) UpdateProfile(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if user exists
	var user models.User
	if err := h.db.First(&user, "id = ?", authUser.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "User not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch user")
	}

	// Bind request
	var req models.UpdateProfileRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Update only provided fields
	updates := make(map[string]interface{})
	if req.Phone != "" {
		updates["phone"] = req.Phone
	}
	if req.JobTitle != "" {
		updates["job_title"] = req.JobTitle
	}
	if req.Department != "" {
		updates["department"] = req.Department
	}
	if req.Timezone != "" {
		updates["timezone"] = req.Timezone
	}
	if req.Bio != "" {
		updates["bio"] = req.Bio
	}

	// Update user
	if len(updates) > 0 {
		if err := h.db.Model(&user).Updates(updates).Error; err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update profile")
		}
	}

	// Fetch updated user
	if err := h.db.First(&user, "id = ?", authUser.ID).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch updated profile")
	}

	return c.JSON(http.StatusOK, user)
}

func (h *UserHandler) GetTeamMembers(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin or manager
	if authUser.Role != "admin" && authUser.Role != "manager" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins and managers can view team members")
	}

	// Parse filters
	var filters models.TeamMemberFilters
	if err := c.Bind(&filters); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid query parameters")
	}

	// Set defaults
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.Limit <= 0 {
		filters.Limit = 10
	}

	// Build query
	query := h.db.Model(&models.User{})

	// Add filters
	if filters.Search != "" {
		searchTerm := "%" + filters.Search + "%"
		query = query.Where("name ILIKE ? OR email ILIKE ?", searchTerm, searchTerm)
	}

	if filters.Role != "" && filters.Role != "all" {
		query = query.Where("role = ?", filters.Role)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to count team members")
	}

	// Get paginated results
	var users []models.User
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Offset(offset).Limit(filters.Limit).Order("name ASC").Find(&users).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch team members")
	}

	// Convert to response format
	members := make([]models.TeamMemberResponse, len(users))
	for i, user := range users {
		members[i] = models.TeamMemberResponse{
			ID:         user.ID.String(),
			Name:       user.Name,
			Email:      user.Email,
			Role:       user.Role,
			Status:     user.Status,
			LastLogin:  formatLastLogin(user.LastLogin),
			JoinedDate: user.JoinedDate.Format("2006-01-02"),
			Avatar:     generateAvatar(user.Name),
		}
	}

	response := models.TeamMembersListResponse{
		Members: members,
		Total:   total,
		Page:    filters.Page,
		Limit:   filters.Limit,
	}

	return c.JSON(http.StatusOK, response)
}

func (h *UserHandler) CreateTeamMember(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins can add team members")
	}

	// Bind request
	var req models.CreateTeamMemberRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Validate required fields
	if req.Name == "" || req.Email == "" || req.Role == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Name, email, and role are required")
	}

	// Validate role
	if !isValidRole(req.Role) {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid role. Must be one of: admin, manager, agent")
	}

	// Check if email already exists
	var existingUser models.User
	if err := h.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return echo.NewHTTPError(http.StatusConflict, "User with this email already exists")
	}

	// Create new user
	user := models.User{
		Name:       req.Name,
		Email:      req.Email,
		Role:       req.Role,
		Status:     "active",
		JoinedDate: time.Now(),
	}

	if err := h.db.Create(&user).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create team member")
	}

	// Return created user
	response := models.TeamMemberResponse{
		ID:         user.ID.String(),
		Name:       user.Name,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		LastLogin:  formatLastLogin(user.LastLogin),
		JoinedDate: user.JoinedDate.Format("2006-01-02"),
		Avatar:     generateAvatar(user.Name),
	}

	return c.JSON(http.StatusCreated, response)
}

func (h *UserHandler) UpdateTeamMember(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins can update team members")
	}

	// Get member ID from URL
	memberID := c.Param("id")
	memberUUID, err := uuid.Parse(memberID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid member ID format")
	}

	// Check if member exists
	var member models.User
	if err := h.db.First(&member, "id = ?", memberUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Team member not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch team member")
	}

	// Bind request
	var req models.UpdateTeamMemberRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Update only provided fields
	updates := make(map[string]interface{})
	if req.Role != "" {
		if !isValidRole(req.Role) {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid role. Must be one of: admin, manager, agent")
		}
		updates["role"] = req.Role
	}
	if req.Status != "" {
		if !isValidUserStatus(req.Status) {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid status. Must be one of: active, inactive")
		}
		updates["status"] = req.Status
	}

	// Update member
	if len(updates) > 0 {
		if err := h.db.Model(&member).Updates(updates).Error; err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update team member")
		}
	}

	// Fetch updated member
	if err := h.db.First(&member, "id = ?", memberUUID).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch updated team member")
	}

	// Return updated member
	response := models.TeamMemberResponse{
		ID:         member.ID.String(),
		Name:       member.Name,
		Email:      member.Email,
		Role:       member.Role,
		Status:     member.Status,
		LastLogin:  formatLastLogin(member.LastLogin),
		JoinedDate: member.JoinedDate.Format("2006-01-02"),
		Avatar:     generateAvatar(member.Name),
	}

	return c.JSON(http.StatusOK, response)
}

func (h *UserHandler) DeleteTeamMember(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins can delete team members")
	}

	// Get member ID from URL
	memberID := c.Param("id")
	memberUUID, err := uuid.Parse(memberID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid member ID format")
	}

	// Check if member exists
	var member models.User
	if err := h.db.First(&member, "id = ?", memberUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Team member not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch team member")
	}

	// Soft delete (set status to inactive)
	if err := h.db.Model(&member).Update("status", "inactive").Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete team member")
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Team member deleted successfully"})
}

// Helper functions
func formatLastLogin(lastLogin *time.Time) string {
	if lastLogin == nil {
		return "Never"
	}
	return lastLogin.Format("2006-01-02 15:04 PM")
}

func generateAvatar(name string) string {
	if name == "" {
		return "??"
	}
	parts := strings.Fields(name)
	if len(parts) == 1 {
		return strings.ToUpper(name[:2])
	}
	return strings.ToUpper(string(parts[0][0]) + string(parts[1][0]))
}

func isValidRole(role string) bool {
	for _, validRole := range models.UserRoles {
		if role == validRole {
			return true
		}
	}
	return false
}

func isValidUserStatus(status string) bool {
	for _, validStatus := range models.UserStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}
