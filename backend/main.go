package main

import (
	"log"
	"net/http"

	"enquiry-management-system/config"
	"enquiry-management-system/database"
	"enquiry-management-system/routes"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database with connection pooling
	dbConn, err := database.NewConnection(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer dbConn.Close() // Ensure connections are closed on shutdown

	// Get the underlying GORM DB for handlers
	db := dbConn.DB

	// Initialize Echo
	e := echo.New()

	// Middleware
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	// Health check endpoint
	e.GET("/health", func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]string{
			"status": "ok",
			"service": "enquiry-management-api",
		})
	})

	// Store database in context for handlers
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("db", db)
			return next(c)
		}
	})

	// Setup routes with authentication
	routes.SetupEnquiryRoutes(e, db, cfg)
	routes.SetupDashboardRoutes(e, db)
	routes.SetupUserRoutes(e, db, cfg)

	// Start server
	log.Printf("Server starting on port %s", cfg.Port)
	log.Fatal(e.Start(":" + cfg.Port))
}
