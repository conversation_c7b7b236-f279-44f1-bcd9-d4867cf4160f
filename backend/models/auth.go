package models

import (
	"github.com/golang-jwt/jwt"
	"github.com/google/uuid"
)

// JWTClaims represents the JWT token claims
type JWTClaims struct {
	UserID string `json:"sub"`
	Email  string `json:"email"`
	Role   string `json:"role"`
	Name   string `json:"name"`
	jwt.StandardClaims
}

// AuthUser represents the authenticated user context
type AuthUser struct {
	ID    uuid.UUID `json:"id"`
	Email string    `json:"email"`
	Role  string    `json:"role"`
	Name  string    `json:"name"`
}

// HasRole checks if the user has a specific role
func (u *AuthUser) HasRole(role string) bool {
	return u.Role == role
}

// HasAnyRole checks if the user has any of the specified roles
func (u *AuthUser) HasAnyRole(roles ...string) bool {
	for _, role := range roles {
		if u.Role == role {
			return true
		}
	}
	return false
}

// IsAdmin checks if the user is an admin
func (u *AuthUser) IsAdmin() bool {
	return u.Role == "admin"
}

// IsManager checks if the user is a manager
func (u *AuthUser) IsManager() bool {
	return u.Role == "manager"
}

// IsAgent checks if the user is an agent
func (u *AuthUser) IsAgent() bool {
	return u.Role == "agent"
}

// IsAdminOrManager checks if the user is admin or manager
func (u *AuthUser) IsAdminOrManager() bool {
	return u.Role == "admin" || u.Role == "manager"
}

// AuthError represents authentication errors
type AuthError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (e AuthError) Error() string {
	return e.Message
}

// Predefined auth errors
var (
	ErrMissingToken     = AuthError{Code: "missing_token", Message: "Authorization token is required"}
	ErrInvalidToken     = AuthError{Code: "invalid_token", Message: "Invalid or malformed token"}
	ErrExpiredToken     = AuthError{Code: "expired_token", Message: "Token has expired"}
	ErrInvalidSignature = AuthError{Code: "invalid_signature", Message: "Invalid token signature"}
	ErrUserNotFound     = AuthError{Code: "user_not_found", Message: "User not found in database"}
	ErrUserInactive     = AuthError{Code: "user_inactive", Message: "User account is inactive"}
	ErrInsufficientRole = AuthError{Code: "insufficient_role", Message: "Insufficient permissions for this action"}
	ErrInvalidIssuer    = AuthError{Code: "invalid_issuer", Message: "Invalid token issuer"}
	ErrInvalidAudience  = AuthError{Code: "invalid_audience", Message: "Invalid token audience"}
)
