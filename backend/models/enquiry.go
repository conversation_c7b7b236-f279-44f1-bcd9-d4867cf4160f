package models

import (
	"time"

	"github.com/google/uuid"
)

type Enquiry struct {
	BaseModel
	Slug        string     `json:"slug" gorm:"uniqueIndex;not null"`
	Name        string     `json:"name" gorm:"not null"`
	Email       string     `json:"email" gorm:"not null"`
	Phone       string     `json:"phone"`
	AssignedToID *uuid.UUID `json:"assigned_to_id" gorm:"type:uuid;index"`
	Status      string     `json:"status" gorm:"not null;default:New"`
	Priority    string     `json:"priority" gorm:"not null;default:Medium"`
	Source      string     `json:"source"`
	Description string     `json:"description" gorm:"type:text"`
	SubmittedOn time.Time  `json:"submitted_on" gorm:"not null;default:CURRENT_TIMESTAMP"`
}

type CreateEnquiryRequest struct {
	Name        string `json:"name" validate:"required"`
	Email       string `json:"email" validate:"required,email"`
	Phone       string `json:"phone"`
	Priority    string `json:"priority"`
	Source      string `json:"source"`
	Description string `json:"description"`
}

type UpdateEnquiryRequest struct {
	AssignedToID *uuid.UUID `json:"assigned_to_id"`
	Status       string     `json:"status"`
	Priority     string     `json:"priority"`
	Source       string     `json:"source"`
}

type EnquiryFilters struct {
	Search       string `query:"search"`
	Status       string `query:"status"`
	Priority     string `query:"priority"`
	AssignedToID string `query:"assigned_to_id"`
	Page         int    `query:"page"`
	Limit        int    `query:"limit"`
}

// TableName specifies the table name for the Enquiry model
func (Enquiry) TableName() string {
	return "enquiries"
}

// Predefined values
var (
	EnquiryStatuses = []string{"New", "In Progress", "Closed"}
	Priorities      = []string{"High", "Medium", "Low"}
	Sources         = []string{"Website Form", "Phone Call", "Email", "Referral", "Social Media"}
)
