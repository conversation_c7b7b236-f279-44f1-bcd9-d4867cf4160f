package routes

import (
	"enquiry-management-system/handlers"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupDashboardRoutes sets up all dashboard-related routes
func SetupDashboardRoutes(e *echo.Echo, db *gorm.DB) {
	// Initialize dashboard handler
	dashboardHandler := handlers.NewDashboardHandler(db)

	// API routes (no auth middleware for testing)
	api := e.Group("/api/v1")
	
	// Dashboard endpoints
	api.GET("/dashboard/overview", dashboardHandler.GetDashboardOverview)

	// TODO: Add auth middleware when ready
	// authGroup := api.Group("", authMiddleware)
	// authGroup.GET("/dashboard/overview", dashboardHandler.GetDashboardOverview)
}
