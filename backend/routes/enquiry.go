package routes

import (
	"enquiry-management-system/config"
	"enquiry-management-system/handlers"
	"enquiry-management-system/middleware"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupEnquiryRoutes sets up all enquiry-related routes
func SetupEnquiryRoutes(e *echo.Echo, db *gorm.DB, cfg *config.Config) {
	// Initialize handlers
	enquiryHandler := handlers.NewEnquiryHandler(db)
	noteHandler := handlers.NewNoteHandler(db)
	activityHandler := handlers.NewActivityHandler(db)

	// API base group
	api := e.Group("/api/v1")

	// Protected enquiry endpoints (authentication required)
	protected := api.Group("", middleware.RequireAuth(cfg, db))
	protected.GET("/enquiries", enquiryHandler.GetEnquiries)
	protected.GET("/enquiries/:id", enquiryHandler.GetEnquiry)
	protected.POST("/enquiries", enquiryHandler.CreateEnquiry)
	protected.PUT("/enquiries/:id", enquiryHandler.UpdateEnquiry)

	// Admin-only endpoints
	adminOnly := api.Group("", middleware.RequireAdmin(cfg, db))
	adminOnly.DELETE("/enquiries/:id", enquiryHandler.DeleteEnquiry)

	// Notes endpoints (authentication required)
	protected.POST("/enquiries/:id/notes", noteHandler.CreateNote)
	protected.GET("/enquiries/:id/notes", noteHandler.GetNotes)
	protected.DELETE("/enquiries/:id/notes/:note_id", noteHandler.DeleteNote)

	// Activity endpoints (authentication required)
	protected.GET("/enquiries/:id/activities", activityHandler.GetActivities)
}
