package routes

import (
	"enquiry-management-system/config"
	"enquiry-management-system/handlers"
	"enquiry-management-system/middleware"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupUserRoutes sets up all user profile related routes
func SetupUserRoutes(e *echo.Echo, db *gorm.DB, cfg *config.Config) {
	// Initialize user handler
	userHandler := handlers.NewUserHandler(db)

	// API routes with authentication
	api := e.Group("/api/v1")

	// User endpoints (require authentication)
	authGroup := api.Group("", middleware.RequireAuth(cfg, db))
	authGroup.GET("/users/me", userHandler.GetMe)
	authGroup.GET("/profile", userHandler.GetProfile)
	authGroup.PUT("/profile", userHandler.UpdateProfile)

	// Team management endpoints (require admin/manager roles)
	adminGroup := api.Group("", middleware.RequireAdminOrManager(cfg, db))
	adminGroup.GET("/team/members", userHandler.GetTeamMembers)

	// Only admins can create, update, and delete team members
	adminOnlyGroup := api.Group("", middleware.RequireAdmin(cfg, db))
	adminOnlyGroup.POST("/team/members", userHandler.CreateTeamMember)
	adminOnlyGroup.PUT("/team/members/:id", userHandler.UpdateTeamMember)
	adminOnlyGroup.DELETE("/team/members/:id", userHandler.DeleteTeamMember)
}
