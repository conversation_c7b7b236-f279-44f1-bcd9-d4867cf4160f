# Supabase Authentication Setup Guide

This guide will help you set up Supabase authentication with Google OAuth for the CRM Dashboard.

## Prerequisites

1. A Supabase account (sign up at [supabase.com](https://supabase.com))
2. A Google Cloud Console account for OAuth setup

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - Name: `CRM Dashboard`
   - Database Password: (generate a secure password)
   - Region: (choose closest to your users)
5. Click "Create new project"

## Step 2: Get Supabase Credentials

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Anon public key** (starts with `eyJ`)

## Step 3: Configure Environment Variables

1. In your project root, update the `.env.local` file:

```env
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Step 4: Set Up Google OAuth

### 4.1 Create Google OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API:
   - Go to **APIs & Services** > **Library**
   - Search for "Google+ API" and enable it
4. Create OAuth credentials:
   - Go to **APIs & Services** > **Credentials**
   - Click **Create Credentials** > **OAuth client ID**
   - Choose **Web application**
   - Add authorized redirect URIs:
     - `https://your-project-ref.supabase.co/auth/v1/callback`
     - `http://localhost:3000/auth/callback` (for development)

### 4.2 Configure Supabase Authentication

1. In your Supabase dashboard, go to **Authentication** > **Providers**
2. Find **Google** and click to configure
3. Enable Google provider
4. Enter your Google OAuth credentials:
   - **Client ID**: From Google Cloud Console
   - **Client Secret**: From Google Cloud Console
5. Click **Save**

## Step 5: Configure Site URL

1. In Supabase dashboard, go to **Authentication** > **URL Configuration**
2. Set **Site URL** to: `http://localhost:3000` (for development)
3. Add **Redirect URLs**:
   - `http://localhost:3000/auth/callback`
   - `http://localhost:3000` (for production, use your domain)

## Step 6: Test Authentication

1. Start your development server: `npm run dev`
2. Navigate to `http://localhost:3000/login`
3. Click "Continue with Google"
4. Complete the OAuth flow
5. You should be redirected to the dashboard

## Production Deployment

For production deployment:

1. Update environment variables with production URLs
2. Update Google OAuth redirect URIs with production domain
3. Update Supabase Site URL and Redirect URLs with production domain
4. Ensure HTTPS is enabled for your production domain

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI"**
   - Check that redirect URIs match exactly in Google Cloud Console
   - Ensure no trailing slashes

2. **"Authentication failed"**
   - Verify environment variables are correct
   - Check that Google OAuth is properly configured in Supabase

3. **"User not found after redirect"**
   - Check middleware configuration
   - Verify callback route is working

### Debug Steps

1. Check browser console for errors
2. Verify environment variables are loaded
3. Check Supabase logs in dashboard
4. Test with different browsers/incognito mode

## Security Notes

- Never commit `.env.local` to version control
- Use different Supabase projects for development and production
- Regularly rotate API keys and OAuth secrets
- Enable RLS (Row Level Security) for database tables

## Additional Resources

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
