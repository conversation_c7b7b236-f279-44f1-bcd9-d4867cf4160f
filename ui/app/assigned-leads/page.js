import { CRMLayout } from "@/components/layout/CRMLayout";
import { AssignedLeadsTable } from "@/components/assigned-leads/AssignedLeadsTable";
import { AssignedLeadsFilters } from "@/components/assigned-leads/AssignedLeadsFilters";
import { AssignedLeadsStats } from "@/components/assigned-leads/AssignedLeadsStats";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { Button } from "@/components/ui/button";
import { Plus, UserPlus } from "lucide-react";

export default function AssignedLeadsPage() {
  return (
    <ProtectedRoute>
      <CRMLayout title="Assigned Leads">
        <div className="p-6">
          {/* Page Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-lg font-medium text-gray-900">Assigned Leads</h2>
              <p className="text-sm text-gray-600">Manage and track leads assigned to team members</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <UserPlus className="h-4 w-4 mr-2" />
                Assign Lead
              </Button>
              <Button className="bg-primary hover:bg-primary/90">
                <Plus className="h-4 w-4 mr-2" />
                New Lead
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <AssignedLeadsStats />

          {/* Filters */}
          <AssignedLeadsFilters />

          {/* Assigned Leads Table */}
          <AssignedLeadsTable />
        </div>
      </CRMLayout>
    </ProtectedRoute>
  );
}
