import { CRMLayout } from "@/components/layout/CRMLayout";
import { EnquiryDetails } from "@/components/enquiries/EnquiryDetails";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { mockEnquiries } from "@/lib/types";
import { notFound } from "next/navigation";

// This function generates static params for all enquiry slugs
export async function generateStaticParams() {
  return mockEnquiries.map((enquiry) => ({
    slug: enquiry.slug,
  }));
}

export default async function EnquiryDetailsPage({ params }) {
  const { slug } = await params;

  // Find the enquiry by slug
  const enquiry = mockEnquiries.find((e) => e.slug === slug);

  // If enquiry not found, show 404
  if (!enquiry) {
    notFound();
  }

  return (
    <ProtectedRoute>
      <CRMLayout title={`Enquiry - ${enquiry.name}`}>
        <EnquiryDetails enquiry={enquiry} />
      </CRMLayout>
    </ProtectedRoute>
  );
}
