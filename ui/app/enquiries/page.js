import { CRMLayout } from "@/components/layout/CRMLayout";
import { EnquiriesTable } from "@/components/enquiries/EnquiriesTable";
import { EnquiriesFilters } from "@/components/enquiries/EnquiriesFilters";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";

export default function EnquiriesPage() {
  return (
    <ProtectedRoute>
      <CRMLayout title="Enquiries">
        <div className="p-6">
          {/* Page Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-lg font-medium text-gray-900">All Enquiries</h2>
              <p className="text-sm text-gray-600">Manage and track all customer enquiries</p>
            </div>
            <Button className="bg-primary hover:bg-primary/90">
              <Plus className="h-4 w-4 mr-2" />
              New Enquiry
            </Button>
          </div>

          {/* Filters */}
          <EnquiriesFilters />

          {/* Enquiries Table */}
          <EnquiriesTable />
        </div>
      </CRMLayout>
    </ProtectedRoute>
  );
}
