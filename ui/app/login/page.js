import { LoginForm } from "@/components/auth/LoginForm";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";

export default function LoginPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-accent/10 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo/Brand */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary mb-2">CRM Dashboard</h1>
          <p className="text-gray-600">Sign in to manage your enquiries and leads</p>
        </div>

        {/* Login Card */}
        <Card className="shadow-lg border-0">
          <CardHeader className="text-center pb-4">
            <CardTitle className="text-xl font-semibold text-gray-900">
              Welcome Back
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Sign in to your account to continue
            </p>
          </CardHeader>
          <CardContent>
            <LoginForm />
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>© 2024 CRM Dashboard. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}
