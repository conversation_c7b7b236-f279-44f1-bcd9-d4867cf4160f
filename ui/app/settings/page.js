import { CRMLayout } from "@/components/layout/CRMLayout";
import { SettingsTabs } from "@/components/settings/SettingsTabs";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";

export default function SettingsPage() {
  return (
    <ProtectedRoute>
      <CRMLayout title="Settings">
        <div className="p-6">
          {/* Page Header */}
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900">Settings</h2>
            <p className="text-sm text-gray-600">Manage your account, team, and system preferences</p>
          </div>

          {/* Settings Tabs */}
          <SettingsTabs />
        </div>
      </CRMLayout>
    </ProtectedRoute>
  );
}
