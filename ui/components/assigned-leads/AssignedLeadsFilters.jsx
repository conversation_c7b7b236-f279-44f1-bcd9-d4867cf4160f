import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Filter, X, Calendar } from "lucide-react";
import { LeadStatus, Sector, teamMembers } from "@/lib/types";

export function AssignedLeadsFilters() {
  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Search */}
          <div className="relative flex-1 min-w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search assigned leads..."
              className="pl-10"
            />
          </div>

          {/* Assigned To Filter */}
          <Select>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Assigned To" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Team Members</SelectItem>
              {teamMembers.map((member) => (
                <SelectItem key={member.id} value={member.name}>
                  {member.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Status Filter */}
          <Select>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value={LeadStatus.NEW}>{LeadStatus.NEW}</SelectItem>
              <SelectItem value={LeadStatus.IN_PROGRESS}>{LeadStatus.IN_PROGRESS}</SelectItem>
              <SelectItem value={LeadStatus.CLOSED}>{LeadStatus.CLOSED}</SelectItem>
            </SelectContent>
          </Select>

          {/* Sector Filter */}
          <Select>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Sector" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Sectors</SelectItem>
              <SelectItem value={Sector.COLLEGE}>{Sector.COLLEGE}</SelectItem>
              <SelectItem value={Sector.BUSINESS}>{Sector.BUSINESS}</SelectItem>
            </SelectContent>
          </Select>

          {/* Priority Filter */}
          <Select>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>

          {/* Date Filter */}
          <Button variant="outline" size="icon">
            <Calendar className="h-4 w-4" />
          </Button>

          {/* Filter Button */}
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>

          {/* Clear Filters */}
          <Button variant="ghost" size="sm">
            <X className="h-4 w-4 mr-1" />
            Clear
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
