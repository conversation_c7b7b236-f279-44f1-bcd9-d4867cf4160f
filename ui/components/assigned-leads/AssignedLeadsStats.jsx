import { Card, CardContent } from "@/components/ui/card";
import { mockAssignedLeads, LeadStatus } from "@/lib/types";
import { Users, Clock, CheckCircle, TrendingUp } from "lucide-react";

export function AssignedLeadsStats() {
  // Calculate stats from mock data
  const totalLeads = mockAssignedLeads.length;
  const newLeads = mockAssignedLeads.filter(lead => lead.status === LeadStatus.NEW).length;
  const inProgressLeads = mockAssignedLeads.filter(lead => lead.status === LeadStatus.IN_PROGRESS).length;
  const closedLeads = mockAssignedLeads.filter(lead => lead.status === LeadStatus.CLOSED).length;

  const statsConfig = [
    {
      id: 'total-assigned',
      title: 'Total Assigned',
      value: totalLeads,
      icon: Users,
      bgColor: 'bg-primary',
      textColor: 'text-white',
      iconColor: 'text-white'
    },
    {
      id: 'new-leads',
      title: 'New Leads',
      value: newLeads,
      icon: Clock,
      bgColor: 'bg-white',
      textColor: 'text-gray-900',
      iconColor: 'text-accent'
    },
    {
      id: 'in-progress',
      title: 'In Progress',
      value: inProgressLeads,
      icon: TrendingUp,
      bgColor: 'bg-white',
      textColor: 'text-gray-900',
      iconColor: 'text-blue-600'
    },
    {
      id: 'completed',
      title: 'Completed',
      value: closedLeads,
      icon: CheckCircle,
      bgColor: 'bg-white',
      textColor: 'text-gray-900',
      iconColor: 'text-green-600'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statsConfig.map((stat) => {
        const Icon = stat.icon;
        return (
          <Card key={stat.id} className={`${stat.bgColor} border-0 shadow-sm`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <h3 className={`text-sm font-medium ${stat.textColor} opacity-80`}>
                    {stat.title}
                  </h3>
                  <p className={`text-3xl font-bold ${stat.textColor}`}>
                    {stat.value}
                  </p>
                </div>
                <Icon className={`h-8 w-8 ${stat.iconColor}`} />
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
