import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Eye, Edit, MoreHorizontal, Phone, Mail, Calendar } from "lucide-react";
import { mockAssignedLeads, LeadStatus, teamMembers } from "@/lib/types";

const getStatusVariant = (status) => {
  switch (status) {
    case LeadStatus.NEW:
      return "bg-accent text-white";
    case LeadStatus.IN_PROGRESS:
      return "bg-blue-100 text-blue-800";
    case LeadStatus.CLOSED:
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getPriorityVariant = (priority) => {
  switch (priority.toLowerCase()) {
    case 'high':
      return "bg-red-100 text-red-800";
    case 'medium':
      return "bg-yellow-100 text-yellow-800";
    case 'low':
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getTeamMemberAvatar = (name) => {
  const member = teamMembers.find(m => m.name === name);
  return member ? member.avatar : name.split(' ').map(n => n[0]).join('');
};

export function AssignedLeadsTable() {
  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="border-gray-200">
              <TableHead className="text-gray-600 font-medium">Lead</TableHead>
              <TableHead className="text-gray-600 font-medium">Assigned To</TableHead>
              <TableHead className="text-gray-600 font-medium">Status</TableHead>
              <TableHead className="text-gray-600 font-medium">Priority</TableHead>
              <TableHead className="text-gray-600 font-medium">Progress</TableHead>
              <TableHead className="text-gray-600 font-medium">Next Follow-up</TableHead>
              <TableHead className="text-gray-600 font-medium">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockAssignedLeads.map((lead) => (
              <TableRow key={lead.id} className="border-gray-100 hover:bg-gray-50">
                <TableCell>
                  <div className="space-y-1">
                    <p className="font-medium text-gray-900">{lead.name}</p>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Mail className="h-3 w-3" />
                      {lead.email}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Phone className="h-3 w-3" />
                      {lead.phone}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-primary/10 text-primary text-xs">
                        {getTeamMemberAvatar(lead.assignedTo)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{lead.assignedTo}</p>
                      <p className="text-xs text-gray-500">{lead.assignedDate}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={getStatusVariant(lead.status)}>
                    {lead.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge className={getPriorityVariant(lead.priority)}>
                    {lead.priority}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-600">Progress</span>
                      <span className="font-medium">{lead.progress}%</span>
                    </div>
                    <Progress value={lead.progress} className="h-2" />
                  </div>
                </TableCell>
                <TableCell>
                  {lead.nextFollowUp ? (
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Calendar className="h-3 w-3" />
                      {lead.nextFollowUp}
                    </div>
                  ) : (
                    <span className="text-xs text-gray-400">No follow-up</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
