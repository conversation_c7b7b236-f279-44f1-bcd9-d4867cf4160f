"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/lib/auth/AuthContext";
import { Loader2, Chrome } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export function LoginForm() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { signInWithGoogle } = useAuth();
  const router = useRouter();

  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);
      setError(null);
      await signInWithGoogle();
      // The redirect will be handled by the OAuth flow
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message || 'An error occurred during sign in');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Error <PERSON>ert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Google Sign In Button */}
      <Button
        onClick={handleGoogleSignIn}
        disabled={loading}
        className="w-full h-12 bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-sm"
        variant="outline"
      >
        {loading ? (
          <Loader2 className="h-5 w-5 animate-spin mr-2" />
        ) : (
          <Chrome className="h-5 w-5 mr-2 text-blue-600" />
        )}
        {loading ? 'Signing in...' : 'Continue with Google'}
      </Button>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-200" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-white px-2 text-gray-500">Secure Authentication</span>
        </div>
      </div>

      {/* Features */}
      <div className="space-y-3">
        <div className="flex items-center text-sm text-gray-600">
          <div className="w-2 h-2 bg-accent rounded-full mr-3"></div>
          <span>Secure OAuth 2.0 authentication</span>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <div className="w-2 h-2 bg-accent rounded-full mr-3"></div>
          <span>Access to all CRM features</span>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <div className="w-2 h-2 bg-accent rounded-full mr-3"></div>
          <span>Real-time data synchronization</span>
        </div>
      </div>

      {/* Help Text */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          By signing in, you agree to our{' '}
          <a href="#" className="text-primary hover:underline">Terms of Service</a>
          {' '}and{' '}
          <a href="#" className="text-primary hover:underline">Privacy Policy</a>
        </p>
      </div>
    </div>
  );
}
