# Authentication System

This directory contains the authentication system for the CRM Dashboard using Supabase and Google OAuth.

## Components

### AuthContext.jsx
React context provider that manages authentication state throughout the application.

**Features:**
- User session management
- Google OAuth sign-in
- Sign-out functionality
- Demo mode support
- Loading states

**Usage:**
```jsx
import { useAuth } from '@/lib/auth/AuthContext';

function MyComponent() {
  const { user, loading, signInWithGoogle, signOut } = useAuth();
  
  if (loading) return <div>Loading...</div>;
  if (!user) return <div>Please sign in</div>;
  
  return <div>Welcome, {user.email}!</div>;
}
```

### LoginForm.jsx
Login form component with Google Sign-In button.

**Features:**
- Google OAuth integration
- Error handling
- Loading states
- Demo mode alerts

### ProtectedRoute.jsx
Higher-order component that protects routes requiring authentication.

**Features:**
- Automatic redirect to login for unauthenticated users
- Loading state handling
- Route protection

**Usage:**
```jsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  );
}
```

## Authentication Flow

### 1. Initial Load
- AuthProvider checks for existing session
- Sets loading state while checking
- Updates user state based on session

### 2. Sign In Process
- User clicks "Continue with Google"
- Redirects to Google OAuth
- Google redirects back to `/auth/callback`
- Callback route exchanges code for session
- User is redirected to dashboard

### 3. Protected Routes
- Middleware checks authentication on each request
- Unauthenticated users redirected to `/login`
- Authenticated users accessing `/login` redirected to dashboard

### 4. Sign Out
- Clears Supabase session
- Updates user state to null
- User redirected to login page

## Configuration

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Supabase Setup
1. Create Supabase project
2. Enable Google OAuth provider
3. Configure redirect URLs
4. Update environment variables

See `SUPABASE_SETUP.md` for detailed instructions.

## Security Features

### Middleware Protection
- Server-side route protection
- Automatic redirects
- Session validation

### Client-side Protection
- ProtectedRoute component
- Context-based state management
- Automatic loading states

### Error Handling
- Graceful fallbacks
- Demo mode for development
- Comprehensive error messages

## File Structure

```
components/auth/
├── AuthContext.jsx      # Authentication context provider
├── LoginForm.jsx        # Login form component
├── ProtectedRoute.jsx   # Route protection wrapper
└── README.md           # This documentation

lib/auth/
└── AuthContext.jsx     # Main auth context (symlink)

lib/supabase/
├── client.js           # Browser Supabase client
└── server.js           # Server Supabase client

app/
├── login/
│   └── page.js         # Login page
├── auth/
│   ├── callback/
│   │   └── route.js    # OAuth callback handler
│   └── auth-code-error/
│       └── page.js     # Error page
└── middleware.js       # Route protection middleware
```

## Troubleshooting

### Common Issues

1. **"Missing Supabase environment variables"**
   - Check `.env.local` file exists
   - Verify environment variable names
   - Restart development server

2. **"Authentication failed"**
   - Check Supabase project configuration
   - Verify Google OAuth setup
   - Check redirect URLs

3. **Infinite redirects**
   - Check middleware configuration
   - Verify route protection logic
   - Check for conflicting redirects

### Debug Mode

Enable debug logging by adding to your environment:
```env
NEXT_PUBLIC_DEBUG_AUTH=true
```

## Best Practices

1. **Always use ProtectedRoute** for authenticated pages
2. **Handle loading states** in components
3. **Check user state** before rendering user-specific content
4. **Use middleware** for server-side protection
5. **Test both authenticated and unauthenticated flows**

## Future Enhancements

- [ ] Role-based access control
- [ ] Multi-factor authentication
- [ ] Session timeout handling
- [ ] Remember me functionality
- [ ] Social login providers (GitHub, Microsoft)
- [ ] Password-based authentication option
