import { CRMLayout } from "@/components/layout/CRMLayout";
import { StatsCards } from "./StatsCards";
import { RecentActivityTable } from "./RecentActivityTable";
import { ActivityFeed } from "./ActivityFeed";

export function Dashboard() {
  return (
    <CRMLayout title="Dashboard">
      <div className="p-6">
        {/* Stats Cards */}
        <StatsCards />

        {/* Recent Activity Section */}
        <div className="flex gap-6">
          {/* Recent Activity Table */}
          <RecentActivityTable />

          {/* Activity Feed */}
          <ActivityFeed />
        </div>
      </div>
    </CRMLayout>
  );
}
