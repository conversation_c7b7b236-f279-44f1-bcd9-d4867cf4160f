import { Card, CardContent } from "@/components/ui/card";
import { mockStats } from "@/lib/types";

const statsConfig = [
  {
    id: 'total-leads',
    title: 'Total Leads',
    value: mockStats.totalLeads,
    bgColor: 'bg-primary',
    textColor: 'text-white'
  },
  {
    id: 'new-today',
    title: 'New Today',
    value: mockStats.newToday,
    bgColor: 'bg-white',
    textColor: 'text-gray-900'
  },
  {
    id: 'in-progress',
    title: 'In Progress',
    value: mockStats.inProgress,
    bgColor: 'bg-white',
    textColor: 'text-gray-900'
  },
  {
    id: 'closed',
    title: 'Closed',
    value: mockStats.closed,
    bgColor: 'bg-white',
    textColor: 'text-gray-900'
  }
];

export function StatsCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statsConfig.map((stat) => (
        <Card key={stat.id} className={`${stat.bgColor} border-0 shadow-sm`}>
          <CardContent className="p-6">
            <div className="space-y-2">
              <h3 className={`text-sm font-medium ${stat.textColor} opacity-80`}>
                {stat.title}
              </h3>
              <p className={`text-3xl font-bold ${stat.textColor}`}>
                {stat.value}
              </p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
