import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, Edit, MoreHorizontal } from "lucide-react";
import { mockEnquiries, LeadStatus } from "@/lib/types";
import Link from "next/link";

const getStatusVariant = (status) => {
  switch (status) {
    case LeadStatus.NEW:
      return "bg-accent text-white";
    case LeadStatus.IN_PROGRESS:
      return "bg-blue-100 text-blue-800";
    case LeadStatus.CLOSED:
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getPriorityVariant = (priority) => {
  switch (priority.toLowerCase()) {
    case 'high':
      return "bg-red-100 text-red-800";
    case 'medium':
      return "bg-yellow-100 text-yellow-800";
    case 'low':
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export function EnquiriesTable() {
  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="border-gray-200">
              <TableHead className="text-gray-600 font-medium">Name</TableHead>
              <TableHead className="text-gray-600 font-medium">Email</TableHead>
              <TableHead className="text-gray-600 font-medium">Sector</TableHead>
              <TableHead className="text-gray-600 font-medium">Status</TableHead>
              <TableHead className="text-gray-600 font-medium">Priority</TableHead>
              <TableHead className="text-gray-600 font-medium">Assigned To</TableHead>
              <TableHead className="text-gray-600 font-medium">Submitted</TableHead>
              <TableHead className="text-gray-600 font-medium">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockEnquiries.map((enquiry) => (
              <TableRow key={enquiry.id} className="border-gray-100 hover:bg-gray-50">
                <TableCell className="font-medium text-gray-900">
                  <Link 
                    href={`/enquiries/${enquiry.slug}`}
                    className="hover:text-primary hover:underline"
                  >
                    {enquiry.name}
                  </Link>
                </TableCell>
                <TableCell className="text-gray-600">
                  {enquiry.email}
                </TableCell>
                <TableCell className="text-gray-600">
                  {enquiry.sector}
                </TableCell>
                <TableCell>
                  <Badge className={getStatusVariant(enquiry.status)}>
                    {enquiry.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge className={getPriorityVariant(enquiry.priority)}>
                    {enquiry.priority}
                  </Badge>
                </TableCell>
                <TableCell className="text-gray-600">
                  {enquiry.assignedTo}
                </TableCell>
                <TableCell className="text-gray-600">
                  {enquiry.submittedOn}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Link href={`/enquiries/${enquiry.slug}`}>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
