import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft, 
  Edit, 
  Mail, 
  Phone, 
  Calendar, 
  User, 
  Building, 
  Flag,
  MessageSquare,
  Clock
} from "lucide-react";
import Link from "next/link";
import { LeadStatus } from "@/lib/types";
import { EnquiryNotes } from "./EnquiryNotes";
import { EnquiryActivity } from "./EnquiryActivity";

const getStatusVariant = (status) => {
  switch (status) {
    case LeadStatus.NEW:
      return "bg-accent text-white";
    case LeadStatus.IN_PROGRESS:
      return "bg-blue-100 text-blue-800";
    case LeadStatus.CLOSED:
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getPriorityVariant = (priority) => {
  switch (priority.toLowerCase()) {
    case 'high':
      return "bg-red-100 text-red-800";
    case 'medium':
      return "bg-yellow-100 text-yellow-800";
    case 'low':
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export function EnquiryDetails({ enquiry }) {
  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href="/enquiries">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">{enquiry.name}</h1>
            <p className="text-sm text-gray-600">Enquiry #{enquiry.id}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getStatusVariant(enquiry.status)}>
            {enquiry.status}
          </Badge>
          <Badge className={getPriorityVariant(enquiry.priority)}>
            {enquiry.priority}
          </Badge>
          <Button className="bg-primary hover:bg-primary/90">
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="notes">Notes ({enquiry.notes.length})</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <Card>
                <CardHeader>
                  <CardTitle>Enquiry Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                    <p className="text-gray-600">{enquiry.description}</p>
                  </div>
                  <Separator />
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">Source</h4>
                      <p className="text-gray-600">{enquiry.source}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">Sector</h4>
                      <p className="text-gray-600">{enquiry.sector}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notes">
              <EnquiryNotes notes={enquiry.notes} enquiryId={enquiry.id} />
            </TabsContent>

            <TabsContent value="activity">
              <EnquiryActivity activities={enquiry.activities} />
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{enquiry.email}</p>
                  <p className="text-xs text-gray-500">Email</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{enquiry.phone}</p>
                  <p className="text-xs text-gray-500">Phone</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Assignment & Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Assignment & Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{enquiry.assignedTo}</p>
                  <p className="text-xs text-gray-500">Assigned To</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{enquiry.submittedOn}</p>
                  <p className="text-xs text-gray-500">Submitted On</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Flag className="h-4 w-4 text-gray-400" />
                <div>
                  <Badge className={getPriorityVariant(enquiry.priority)}>
                    {enquiry.priority} Priority
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
