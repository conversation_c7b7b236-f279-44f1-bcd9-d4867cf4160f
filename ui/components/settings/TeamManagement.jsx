"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { mockTeamMembers, userRoles } from "@/lib/types";
import { useAuth } from "@/lib/auth/AuthContext";
import { teamManagementAPI, transformAPIDataToTeamMember, transformFormDataToAPI } from "@/lib/api/teamManagement";
import toast, { Toaster } from 'react-hot-toast';
import {
  UserPlus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Shield,
  Clock,
  Loader2
} from "lucide-react";



export function TeamManagement() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState("all");
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [teamMembers, setTeamMembers] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  });
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "agent"
  });

  // Check if current user has admin permissions
  const isAdmin = user?.role === 'admin' || user?.id === 'demo-user';
  const isManager = user?.role === 'manager' || isAdmin;

  console.log('[TeamManagement] User role check:', {
    hasUser: !!user,
    userId: user?.id,
    userRole: user?.role,
    isAdmin,
    isManager
  });

  // Load team members data
  useEffect(() => {
    loadTeamMembers();
  }, [searchTerm, selectedRole, pagination.page]);

  const loadTeamMembers = async () => {
    console.log('[TeamManagement] Loading team members...');

    try {
      if (!initialLoading) setLoading(true);

      const filters = {
        search: searchTerm || undefined,
        role: selectedRole !== 'all' ? selectedRole : undefined,
        page: pagination.page,
        limit: pagination.limit
      };

      const response = await teamManagementAPI.getTeamMembers(filters);

      // Transform API data to frontend format
      const transformedMembers = response.members.map(transformAPIDataToTeamMember);
      setTeamMembers(transformedMembers);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));

      console.log('[TeamManagement] Team members loaded successfully');

    } catch (error) {
      console.error('[TeamManagement] Failed to load team members:', error);
      toast.error('Failed to load team members. Using demo data.');

      // Fallback to mock data
      const filteredMembers = mockTeamMembers.filter(member => {
        const matchesSearch = !searchTerm ||
          member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.email.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesRole = selectedRole === "all" || member.role === selectedRole;
        return matchesSearch && matchesRole;
      });
      setTeamMembers(filteredMembers);
      setPagination(prev => ({
        ...prev,
        total: filteredMembers.length
      }));
    } finally {
      setLoading(false);
      setInitialLoading(false);
    }
  };

  const handleAddUser = async () => {
    if (!isAdmin) {
      toast.error('Only administrators can add team members');
      return;
    }

    try {
      setLoading(true);

      const apiData = transformFormDataToAPI(newUser);
      await teamManagementAPI.createTeamMember(apiData);

      toast.success('Team member added successfully!');
      setIsAddingUser(false);
      setNewUser({ name: "", email: "", role: "agent" });

      // Reload team members
      await loadTeamMembers();

    } catch (error) {
      console.error('[TeamManagement] Failed to add team member:', error);
      toast.error(error.message || 'Failed to add team member. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateMember = async (memberId, updates) => {
    if (!isAdmin) {
      toast.error('Only administrators can update team members');
      return;
    }

    try {
      setLoading(true);

      await teamManagementAPI.updateTeamMember(memberId, updates);

      toast.success('Team member updated successfully!');

      // Reload team members
      await loadTeamMembers();

    } catch (error) {
      console.error('[TeamManagement] Failed to update team member:', error);
      toast.error(error.message || 'Failed to update team member. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMember = async (memberId, memberName) => {
    if (!isAdmin) {
      toast.error('Only administrators can delete team members');
      return;
    }

    if (!confirm(`Are you sure you want to deactivate ${memberName}? This action can be undone by reactivating the user.`)) {
      return;
    }

    try {
      setLoading(true);

      await teamManagementAPI.deleteTeamMember(memberId);

      toast.success('Team member deactivated successfully!');

      // Reload team members
      await loadTeamMembers();

    } catch (error) {
      console.error('[TeamManagement] Failed to delete team member:', error);
      toast.error(error.message || 'Failed to deactivate team member. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getRoleInfo = (roleId) => {
    return userRoles.find(role => role.id === roleId) || userRoles[0];
  };

  const getRoleVariant = (role) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 hover:bg-red-100';
      case 'manager':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'agent':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  const getStatusVariant = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  // Show loading if user is not loaded yet
  if (!user) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Loading user information...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show access denied for non-admin/manager users
  if (!isManager) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
              <p className="text-gray-600">You don't have permission to view team management.</p>
              <p className="text-sm text-gray-500 mt-1">Contact your administrator for access.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show loading state
  if (initialLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Loading team members...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Toaster position="top-right" />

      {/* Team Members */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Team Members
            {isAdmin && (
              <Dialog open={isAddingUser} onOpenChange={setIsAddingUser}>
                <DialogTrigger asChild>
                  <Button
                    className="bg-primary hover:bg-primary/90"
                    disabled={loading}
                  >
                    {loading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <UserPlus className="h-4 w-4 mr-2" />
                    )}
                    Add Member
                  </Button>
                </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Team Member</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      value={newUser.name}
                      onChange={(e) => setNewUser(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter full name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="Enter email address"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role">Role</Label>
                    <Select value={newUser.role} onValueChange={(value) => setNewUser(prev => ({ ...prev, role: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {userRoles.map((role) => (
                          <SelectItem key={role.id} value={role.id}>
                            {role.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex justify-end gap-2 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setIsAddingUser(false)}
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddUser}
                      className="bg-primary hover:bg-primary/90"
                      disabled={loading || !newUser.name || !newUser.email || !newUser.role}
                    >
                      {loading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        'Add Member'
                      )}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search team members..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedRole} onValueChange={setSelectedRole}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                {userRoles.map((role) => (
                  <SelectItem key={role.id} value={role.id}>
                    {role.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Team Members Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Member</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>Joined</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teamMembers.map((member) => {
                const roleInfo = getRoleInfo(member.role);
                return (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="bg-primary/10 text-primary text-xs">
                            {member.avatar}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-gray-900">{member.name}</p>
                          <p className="text-sm text-gray-500">{member.email}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getRoleVariant(member.role)}>
                        {roleInfo.name}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusVariant(member.status)}>
                        {member.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {member.lastLogin}
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {member.joinedDate}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
