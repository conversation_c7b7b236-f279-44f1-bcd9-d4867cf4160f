/**
 * Team Management API Service
 */

import { createClient } from '@/lib/supabase/client';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * Get authorization headers with JWT token
 */
async function getAuthHeaders() {
  console.log('[TeamAPI] Getting auth headers...');

  try {
    console.log('[TeamAPI] Creating Supabase client...');
    const supabase = createClient();

    console.log('[TeamAPI] Getting session from Supabase...');
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      console.error('[TeamAPI] Error getting session:', error);
      throw error;
    }

    console.log('[TeamAPI] Session data:', {
      hasSession: !!session,
      hasAccessToken: !!session?.access_token,
      tokenLength: session?.access_token?.length
    });

    if (session?.access_token) {
      console.log('[TeamAPI] Found access token, creating authenticated headers');
      return {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      };
    } else {
      console.warn('[TeamAPI] No access token found in session');
      throw new Error('No authentication token available');
    }
  } catch (error) {
    console.error('[TeamAPI] Error in getAuthHeaders:', error);
    throw error;
  }
}

/**
 * Team Management API endpoints
 */
export const teamManagementAPI = {
  /**
   * Get team members with filtering and pagination
   * @param {Object} filters - Filter options
   * @param {string} [filters.search] - Search by name or email
   * @param {string} [filters.role] - Filter by role (admin, manager, agent, or "all")
   * @param {number} [filters.page] - Page number (default: 1)
   * @param {number} [filters.limit] - Items per page (default: 10)
   * @returns {Promise<Object>} Team members list with pagination
   */
  async getTeamMembers(filters = {}) {
    console.log('[TeamAPI] Starting getTeamMembers request with filters:', filters);
    
    const url = new URL(`${API_BASE_URL}/api/v1/team/members`);
    
    // Add query parameters
    if (filters.search) url.searchParams.append('search', filters.search);
    if (filters.role && filters.role !== 'all') url.searchParams.append('role', filters.role);
    if (filters.page) url.searchParams.append('page', filters.page.toString());
    if (filters.limit) url.searchParams.append('limit', filters.limit.toString());
    
    console.log('[TeamAPI] Request URL:', url.toString());
    
    const headers = await getAuthHeaders();
    console.log('[TeamAPI] Request headers:', {
      ...headers,
      Authorization: headers.Authorization ? `Bearer ${headers.Authorization.substring(7, 27)}...` : 'Not present'
    });

    console.log('[TeamAPI] Making fetch request...');
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    console.log('[TeamAPI] Response received:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('[TeamAPI] Error response body:', error);
      throw new Error(error || `HTTP ${response.status}`);
    }

    const data = await response.json();
    console.log('[TeamAPI] Success response data:', data);
    return data;
  },

  /**
   * Create a new team member
   * @param {Object} memberData - Member data
   * @param {string} memberData.name - Full name
   * @param {string} memberData.email - Email address
   * @param {string} memberData.role - Role (admin, manager, agent)
   * @returns {Promise<Object>} Created team member
   */
  async createTeamMember(memberData) {
    console.log('[TeamAPI] Starting createTeamMember request with data:', memberData);
    
    const url = `${API_BASE_URL}/api/v1/team/members`;
    console.log('[TeamAPI] Request URL:', url);
    
    const headers = await getAuthHeaders();

    console.log('[TeamAPI] Making fetch request...');
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(memberData),
    });

    console.log('[TeamAPI] Response received:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('[TeamAPI] Error response body:', error);
      throw new Error(error || `HTTP ${response.status}`);
    }

    const data = await response.json();
    console.log('[TeamAPI] Success response data:', data);
    return data;
  },

  /**
   * Update a team member's role or status
   * @param {string} memberId - Member ID (UUID)
   * @param {Object} updates - Updates to apply
   * @param {string} [updates.role] - New role
   * @param {string} [updates.status] - New status
   * @returns {Promise<Object>} Updated team member
   */
  async updateTeamMember(memberId, updates) {
    console.log('[TeamAPI] Starting updateTeamMember request:', { memberId, updates });
    
    const url = `${API_BASE_URL}/api/v1/team/members/${memberId}`;
    console.log('[TeamAPI] Request URL:', url);
    
    const headers = await getAuthHeaders();

    console.log('[TeamAPI] Making fetch request...');
    const response = await fetch(url, {
      method: 'PUT',
      headers,
      body: JSON.stringify(updates),
    });

    console.log('[TeamAPI] Response received:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('[TeamAPI] Error response body:', error);
      throw new Error(error || `HTTP ${response.status}`);
    }

    const data = await response.json();
    console.log('[TeamAPI] Success response data:', data);
    return data;
  },

  /**
   * Delete (deactivate) a team member
   * @param {string} memberId - Member ID (UUID)
   * @returns {Promise<Object>} Success message
   */
  async deleteTeamMember(memberId) {
    console.log('[TeamAPI] Starting deleteTeamMember request for member:', memberId);
    
    const url = `${API_BASE_URL}/api/v1/team/members/${memberId}`;
    console.log('[TeamAPI] Request URL:', url);
    
    const headers = await getAuthHeaders();

    console.log('[TeamAPI] Making fetch request...');
    const response = await fetch(url, {
      method: 'DELETE',
      headers,
    });

    console.log('[TeamAPI] Response received:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('[TeamAPI] Error response body:', error);
      throw new Error(error || `HTTP ${response.status}`);
    }

    const data = await response.json();
    console.log('[TeamAPI] Success response data:', data);
    return data;
  },
};

/**
 * Helper function to transform backend API data to frontend format
 * @param {Object} apiData - Data from the API
 * @returns {Object} Frontend-compatible data
 */
export function transformAPIDataToTeamMember(apiData) {
  return {
    id: apiData.id,
    name: apiData.name,
    email: apiData.email,
    role: apiData.role,
    status: apiData.status,
    lastLogin: apiData.last_login,
    joinedDate: apiData.joined_date,
    avatar: apiData.avatar,
  };
}

/**
 * Helper function to transform frontend form data to backend API format
 * @param {Object} formData - Form data from the UI
 * @returns {Object} API-compatible data
 */
export function transformFormDataToAPI(formData) {
  return {
    name: formData.name,
    email: formData.email,
    role: formData.role,
  };
}
