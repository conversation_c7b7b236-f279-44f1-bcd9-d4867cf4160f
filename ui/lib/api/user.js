/**
 * User API Service
 */

import { createClient } from '@/lib/supabase/client';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * Get authorization headers with JW<PERSON> token
 */
async function getAuthHeaders() {
  const supabase = createClient();
  const { data } = await supabase.auth.getSession();
  const token = data?.session?.access_token;

  if (!token) {
    throw new Error('User is not authenticated. Please sign in.');
  }

  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
}

/**
 * User API endpoints
 */
export const userAPI = {
  /**
   * Get current authenticated user details from users table
   * @returns {Promise<Object>} Complete user data including role, status, etc.
   */
  async getMe() {
    const url = `${API_BASE_URL}/api/v1/users/me`;
    const headers = await getAuthHeaders();

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(error || `HTTP ${response.status}`);
    }

    return await response.json();
  },
};

/**
 * Helper function to transform backend user data to frontend format
 * @param {Object} apiData - Data from the API
 * @returns {Object} Frontend-compatible user data
 */
export function transformUserData(apiData) {
  return {
    id: apiData.id,
    name: apiData.name,
    email: apiData.email,
    role: apiData.role,
    status: apiData.status,
    phone: apiData.phone,
    jobTitle: apiData.job_title,
    department: apiData.department,
    bio: apiData.bio,
    timezone: apiData.timezone,
    avatar: apiData.avatar,
    lastLogin: apiData.last_login,
    joinedDate: apiData.joined_date,
    createdAt: apiData.created_at,
    updatedAt: apiData.updated_at,
  };
}
