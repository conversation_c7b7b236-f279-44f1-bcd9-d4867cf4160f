"use client";

import { createContext, useContext, useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import { userAPI, transformUserData } from '@/lib/api/user';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [accessToken, setAccessToken] = useState(null);
  const [completeUser, setCompleteUser] = useState(null);

  const supabase = createClient();

  // Function to fetch complete user details from our backend
  const fetchCompleteUserDetails = async (userId) => {
    if (!userId || !accessToken) {
      setCompleteUser(null);
      return;
    }

    try {
      const userData = await userAPI.getMe();
      const transformedUser = transformUserData(userData);
      setCompleteUser(transformedUser);
    } catch (error) {
      console.error('Failed to fetch complete user details:', error);
      setCompleteUser(null);
    }
  };

  // Get session and access token
  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  // Get user
  useEffect(() => {
    const getUser = async () => {
      const { data, error } = await supabase.auth.getUser();
      if (error) {
        console.error(error);
        setUser(null);
      } else {
        setUser(data.user);
      }
      setLoading(false);
    };
    getUser();
  }, [supabase.auth]);

  // Fetch complete user details when user and token are available
  useEffect(() => {
    if (user && accessToken) {
      fetchCompleteUserDetails(user.id);
    } else {
      setCompleteUser(null);
    }
  }, [user, accessToken]);

  // Listen for auth changes
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        const supabaseUser = session?.user ?? null;
        const token = session?.access_token ?? null;

        setUser(supabaseUser);
        setAccessToken(token);

        if (!supabaseUser) {
          setCompleteUser(null);
          setLoading(false);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase.auth]);

  const signInWithGoogle = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
    });
    if (error) {
      console.error('Error signing in with Google:', error);
      throw error;
    }
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const value = {
    user: completeUser || user, // Use completeUser if available, fallback to basic user
    basicUser: user, // Keep basic Supabase user for reference
    completeUser,
    accessToken,
    loading,
    signInWithGoogle,
    signOut,
    supabase
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
